// public/background.js
let attachedTabs = {}; // Store { tabId: { debuggerAttached: boolean, localUrl: string } }
let currentSettings = { isEnabled: false, localUrl: '' };
const DEBUGGER_VERSION = "1.3"; // Chrome Debugger Protocol version

// Load initial settings
chrome.storage.local.get(['isEnabled', 'localUrl'], (result) => {
    currentSettings.isEnabled = result.isEnabled || false;
    currentSettings.localUrl = result.localUrl || '';
    console.log("Locolite BG: Initial settings loaded:", currentSettings);
});

// Listen for storage changes (e.g., from popup)
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
        if (changes.isEnabled !== undefined) { // Check if isEnabled actually changed
            const changed = currentSettings.isEnabled !== changes.isEnabled.newValue;
            currentSettings.isEnabled = changes.isEnabled.newValue;
            console.log("Locolite BG: isEnabled changed:", currentSettings.isEnabled);
            // If disabling globally, detach from all tabs
            if (!currentSettings.isEnabled && changed) {
                detachAllDebuggers();
            }
        }
        if (changes.localUrl !== undefined) {
            currentSettings.localUrl = changes.localUrl.newValue;
            console.log("Locolite BG: localUrl changed:", currentSettings.localUrl);
            // Update attached tabs with new URL
            for (const tabId in attachedTabs) {
                if (attachedTabs[tabId]) {
                    attachedTabs[tabId].localUrl = currentSettings.localUrl;
                }
            }
        }
    }
});

// --- Debugger Handling ---

function attachDebugger(tabId) {
    // Ensure we have the *latest* URL before attaching
    const urlToUse = currentSettings.localUrl;
    if (!urlToUse) {
        console.warn("Locolite BG: Cannot attach debugger: Local URL not set.");
        sendStatusUpdate("⚠️ Error: Set Local Dev Server URL first.", true, tabId);
        return Promise.reject("Local URL not set");
    }
    if (attachedTabs[tabId]?.debuggerAttached) {
        console.log(`Locolite BG: Debugger already attached to tab ${tabId}`);
         // Ensure Fetch is enabled even if already attached (might have failed before)
         return enableFetchInterception(tabId);
    }

    console.log(`Locolite BG: Attempting to attach debugger to tab ${tabId}`);
    return new Promise((resolve, reject) => {
        chrome.debugger.attach({ tabId: tabId }, DEBUGGER_VERSION, () => {
            if (chrome.runtime.lastError) {
                console.error(`Locolite BG: Debugger attach error for tab ${tabId}:`, chrome.runtime.lastError.message);
                sendStatusUpdate(`Debugger attach failed: ${chrome.runtime.lastError.message}`, true, tabId);
                delete attachedTabs[tabId]; // Clean up state
                reject(chrome.runtime.lastError.message);
                return;
            }
            console.log(`Locolite BG: Debugger attached successfully to tab ${tabId}`);
            attachedTabs[tabId] = { debuggerAttached: true, localUrl: urlToUse }; // Use current URL

            // Add listeners *after* successful attachment, ensuring they are added only once
            if (!chrome.debugger.onEvent.hasListener(onDebuggerEvent)) {
                chrome.debugger.onEvent.addListener(onDebuggerEvent);
            }
            if (!chrome.debugger.onDetach.hasListener(onDebuggerDetach)) {
                chrome.debugger.onDetach.addListener(onDebuggerDetach);
            }

             enableFetchInterception(tabId)
                 .then(resolve)
                 .catch(reject);
        });
    });
}

function enableFetchInterception(tabId) {
     return new Promise((resolve, reject) => {
          // Enable Fetch domain for network interception
          chrome.debugger.sendCommand({ tabId: tabId }, "Fetch.enable", {
              patterns: [{ resourceType: "Document", requestStage: "Request" }] // Intercept main document requests
          }, () => {
              if (chrome.runtime.lastError) {
                  console.error(`Locolite BG: Fetch.enable error for tab ${tabId}:`, chrome.runtime.lastError.message);
                  sendStatusUpdate(`Fetch interception failed: ${chrome.runtime.lastError.message}`, true, tabId);
                  // Attempt to detach if Fetch.enable failed, but don't let it block the reject
                  chrome.debugger.detach({ tabId: tabId }).catch(()=>{});
                  reject(`Fetch.enable failed: ${chrome.runtime.lastError.message}`);
              } else {
                  console.log(`Locolite BG: Fetch interception enabled for tab ${tabId}`);
                  sendStatusUpdate("Overrides active. Reload page.", false, tabId);
                  resolve();
              }
          });
     });
}


function detachDebugger(tabId) {
    return new Promise((resolve) => {
         if (!attachedTabs[tabId]?.debuggerAttached) {
             // console.log(`Locolite BG: Debugger not attached to tab ${tabId}, skipping detach.`);
             resolve(); // Nothing to do
             return;
         }
        console.log(`Locolite BG: Detaching debugger from tab ${tabId}`);

         // It's generally safer to attempt removal even if unsure if they were added for this specific tab
         chrome.debugger.onEvent.removeListener(onDebuggerEvent);
         chrome.debugger.onDetach.removeListener(onDebuggerDetach);

         chrome.debugger.detach({ tabId: tabId }, () => {
            if (chrome.runtime.lastError) {
                // Log error but resolve anyway, as the debugger might be gone already
                console.error(`Locolite BG: Debugger detach error for tab ${tabId}:`, chrome.runtime.lastError.message);
            } else {
                console.log(`Locolite BG: Debugger detached from tab ${tabId}`);
            }
            delete attachedTabs[tabId];
            // Only send status update if disabling wasn't global, otherwise it's redundant
             if (currentSettings.isEnabled) {
                 sendStatusUpdate("Overrides disabled.", false, tabId);
             }
            resolve();
        });
    });
}

function detachAllDebuggers() {
    console.log("Locolite BG: Detaching from all tabs.");
    const detachPromises = Object.keys(attachedTabs).map(tabIdStr => {
        const tabId = parseInt(tabIdStr, 10);
        if (attachedTabs[tabId]?.debuggerAttached) {
            return detachDebugger(tabId);
        }
        return Promise.resolve();
    });
    return Promise.all(detachPromises).then(() => {
        // Send a general status update after detaching all
        sendStatusUpdate("Overrides disabled globally.", false, null);
    });
}

// --- Event Listeners ---

// Listen for messages from Popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
     console.log("Locolite BG: Received message:", message);
    if (message.action === "enableDebugging") {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs && tabs.length > 0) {
                const tabId = tabs[0].id;
                if (tabId) {
                    attachDebugger(tabId)
                        .then(() => sendResponse({ status: "Debugger attached. Reload page." }))
                        .catch(error => sendResponse({ status: `Error attaching: ${error}` }));
                } else {
                    sendResponse({ status: "Error: Could not get active tab ID." });
                }
            } else {
                sendResponse({ status: "Error: No active tab found." });
            }
        });
        return true; // Indicates async response
    }
    else if (message.action === "disableDebugging") {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs && tabs.length > 0) {
                const tabId = tabs[0].id;
                if (tabId) {
                    detachDebugger(tabId)
                        .then(() => sendResponse({ status: "Debugger detached." }));
                } else {
                     sendResponse({ status: "Error: Could not get active tab ID." });
                }
            } else {
                // If no active tab, maybe we should detach all known tabs?
                 detachAllDebuggers()
                     .then(() => sendResponse({ status: "All debuggers detached." }));
            }
        });
        return true; // Indicates async response
    }
    else if (message.action === "updateSettings") {
        // This action might be redundant now due to storage listener, but can stay
        if (message.localUrl !== undefined) {
            // Handled by storage listener
        }
        sendResponse({ status: "Settings acknowledged." });
     }
     else if (message.action === "getTabStatus") {
         chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
             if (tabs && tabs.length > 0) {
                 const tabId = tabs[0].id;
                 const isAttached = attachedTabs[tabId]?.debuggerAttached ?? false;
                 sendResponse({ isAttached: isAttached });
             } else {
                 sendResponse({ isAttached: false, error: "No active tab" });
             }
         });
         return true; // Async
     }
});

// Listen for Debugger Events (like network requests)
function onDebuggerEvent(debuggeeId, message, params) {
    const tabId = debuggeeId.tabId;
    if (!attachedTabs[tabId] || !currentSettings.isEnabled) return; // Ignore events if not enabled globally or for this tab

    if (message === "Fetch.requestPaused") {
        const { requestId, request, resourceType } = params;

        if (resourceType === "Document") {
            console.log(`Locolite BG: Intercepted Document request: ${request.url} (RequestId: ${requestId})`);
            const tabInfo = attachedTabs[tabId];
            const currentLocalUrl = currentSettings.localUrl; // Use latest URL from settings

            if (!tabInfo || !currentLocalUrl) {
                console.warn("Locolite BG: Skipping override: localUrl not set or tabInfo missing for tab", tabId);
                chrome.debugger.sendCommand({ tabId: tabId }, "Fetch.continueRequest", { requestId }).catch(e => console.error("Error continuing request:", e));;
                return;
            }

            handleInterceptedRequest(tabId, requestId, request.url, currentLocalUrl);
        } else {
            // Let other requests pass through unmodified
            chrome.debugger.sendCommand({ tabId: tabId }, "Fetch.continueRequest", { requestId }).catch(e => console.error("Error continuing non-doc request:", e));
        }
    }
}

// Handle Debugger Detach Event (e.g., user closed DevTools, tab closed)
function onDebuggerDetach(debuggeeId, reason) {
    const tabId = debuggeeId.tabId;
    console.log(`Locolite BG: Debugger detached from tab ${tabId}. Reason: ${reason}`);
    if (attachedTabs[tabId]) {
        delete attachedTabs[tabId];
        // Update popup status ONLY if the extension is still globally enabled
        // And check if the detach reason suggests user action (e.g., closed devtools) vs programmatic detach
         if (currentSettings.isEnabled && reason !== 'target_closed') {
            // Inform the popup that this specific tab was detached, possibly by user action
            sendStatusUpdate("Debugger detached (e.g., DevTools closed). Re-enable toggle if needed.", true, tabId, true);

             // Since debugger detached unexpectedly for the active tab, maybe force the global toggle state off?
             // This prevents the toggle staying 'on' when debugging is actually stopped for the active tab.
             // Let's check if the detached tab is the active one.
             chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                 if (tabs && tabs.length > 0 && tabs[0].id === tabId) {
                     console.log("Locolite BG: Detached from active tab, forcing toggle off in storage.");
                     chrome.storage.local.set({ isEnabled: false }); // This will trigger the storage listener
                 }
             });
         }
    }
}


// --- Core Logic: Modify HTML ---

async function handleInterceptedRequest(tabId, requestId, originalUrl, localDevUrl) {
    console.log(`Locolite BG: Handling intercepted request for ${originalUrl} on tab ${tabId} with local URL ${localDevUrl}`);

    try {
        // 1. Get the original response body
        const response = await chrome.debugger.sendCommand(
            { tabId: tabId },
            "Fetch.getResponseBody",
            { requestId: requestId }
        );

        let body;
        if (response.base64Encoded) {
            // Handle potential encoding issues during atob
            try {
                // This sequence is generally robust for UTF-8
                body = decodeURIComponent(atob(response.body).split('').map(function(c) {
                     return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                 }).join(''));
            } catch (e) {
                 console.warn("Locolite BG: Base64 decoding error, attempting fallback:", e);
                 try { // Simpler fallback
                    body = atob(response.body);
                 } catch (e2) {
                    console.error("Locolite BG: Fallback Base64 decoding failed:", e2);
                    throw new Error("Failed to decode response body");
                 }
            }
        } else {
            body = response.body; // Assume UTF-8 if not base64
        }

         // 2. Modify the HTML body
         // Ensure localDevUrl is a valid base URL (ends with / if needed)
         const validatedLocalUrl = localDevUrl.endsWith('/') ? localDevUrl : localDevUrl + '/';
         const modifiedBody = rewriteAssetPaths(body, originalUrl, validatedLocalUrl);

         // 3. Fulfill the request with the modified body
         // Need to properly encode back to Base64 for the API
         // This sequence handles UTF-8 correctly before btoa
         const encodedBody = btoa(unescape(encodeURIComponent(modifiedBody)));

        await chrome.debugger.sendCommand(
            { tabId: tabId },
            "Fetch.fulfillRequest",
            {
                requestId: requestId,
                responseCode: 200, // Assuming original request was successful
                responseHeaders: [ // Pass minimal headers, browser infers others. Might need more specific ones sometimes.
                    { name: 'Content-Type', value: 'text/html; charset=utf-8' }
                ],
                body: encodedBody
            }
        );
        console.log(`Locolite BG: Fulfilled request ${requestId} with modified content pointing to ${localDevUrl}`);

    } catch (error) {
        console.error(`Locolite BG: Error handling intercepted request ${requestId} for tab ${tabId}:`, error);
        sendStatusUpdate(`Error processing override: ${error.message || error}`, true, tabId);
        // If error occurs, try to let the original request continue
        try {
            await chrome.debugger.sendCommand({ tabId: tabId }, "Fetch.continueRequest", { requestId });
        } catch (continueError) {
            console.error(`Locolite BG: Error trying to continue request ${requestId} after failure:`, continueError);
            // If continuing also fails, fulfill with an error page? Or just let it fail.
             try {
                  await chrome.debugger.sendCommand(
                      { tabId: tabId },
                      "Fetch.failRequest",
                      { requestId: requestId, errorReason: 'Aborted' } // Or another appropriate reason
                  );
              } catch (failError) {
                  console.error(`Locolite BG: Error failing request ${requestId} after primary error:`, failError);
              }
        }
    }
}

function rewriteAssetPaths(htmlContent, originalPageUrlString, localDevBaseUrl) {
    console.log(`Locolite BG: Rewriting assets in HTML for ${originalPageUrlString} to use ${localDevBaseUrl}`);
    const pageUrl = new URL(originalPageUrlString);
    const pageOrigin = pageUrl.origin;

    // More robust regex: Handles single/double quotes, whitespace, case-insensitive
    const modifiedHtml = htmlContent.replace(/(src|href)\s*=\s*(["'])(.+?)\2/gi, (match, attr, quote, value) => {
         if (!value || value.startsWith('data:') || value.startsWith('blob:') || value.startsWith('#') || value.startsWith('javascript:')) {
            return match; // Ignore special protocols and fragments
         }

        try {
             // Resolve the asset URL relative to the *original* page's URL
             const absoluteAssetUrl = new URL(value, pageUrl.href).toString(); // Get absolute URL string

            // --- Replacement Logic ---
            // Replace if the resolved absolute URL starts with the original page's origin
            if (absoluteAssetUrl.startsWith(pageOrigin)) {
                 // Extract the path + query + hash part from the absolute asset URL
                 const pathAndQuery = absoluteAssetUrl.substring(pageOrigin.length); // e.g., "/js/app.js?v=1"

                 // Construct the new URL using the local dev base URL
                 // Ensure no double slashes if localDevBaseUrl ends with / and pathAndQuery starts with /
                 let newUrl;
                 if (localDevBaseUrl.endsWith('/') && pathAndQuery.startsWith('/')) {
                     newUrl = localDevBaseUrl + pathAndQuery.substring(1);
                 } else if (!localDevBaseUrl.endsWith('/') && !pathAndQuery.startsWith('/')) {
                      newUrl = localDevBaseUrl + '/' + pathAndQuery;
                 }
                 else {
                     newUrl = localDevBaseUrl + pathAndQuery;
                 }

                 // Basic check to avoid replacing the page itself if it's linked relatively
                 if (newUrl.replace(/\/$/, '') === originalPageUrlString.replace(/\/$/, '')) {
                    console.log(`  Skipping rewrite for self-reference: ${value}`);
                    return match;
                 }

                 console.log(`  Rewriting ${value} -> ${newUrl}`);
                 return `${attr}=${quote}${newUrl}${quote}`; // Return the modified attribute string
            }

            // Add more rules here? E.g., only replace *.js / *.css?
            // if (absoluteAssetUrl.startsWith(pageOrigin) && /\.(js|css)($|\?)/i.test(absoluteAssetUrl)) { ... }


        } catch (e) {
            console.warn(`Locolite BG: Could not process asset URL: ${value}`, e);
        }

        // If no replacement rule matched, return the original attribute string
        return match;
    });

    // Also replace base tag href if present and points to original origin? Risky maybe.
    // modifiedHtml = modifiedHtml.replace(/<base\s+href\s*=\s*(["'])(.+?)\1/gi, (match, quote, value) => { ... });

    return modifiedHtml;
}


// --- Utility ---
function sendStatusUpdate(status, isError = false, tabId = null, detached = false) {
    // Send to popup (if open)
    console.log(`Locolite BG: Status update${tabId ? ` for tab ${tabId}` : ''}: ${status}`);
    chrome.runtime.sendMessage({
        action: "statusUpdate",
        status: status,
        isError: isError,
        detached: detached // Inform popup if disabling occurred unexpectedly for the tab
    }).catch(e => { /* Ignore error if popup isn't open */ });
}

// --- Cleanup on Extension Uninstall/Disable ---
chrome.runtime.onSuspend.addListener(() => {
    console.log("Locolite BG: Extension suspending. Detaching all debuggers.");
    detachAllDebuggers();
});

// --- Initial check on startup ---
// If extension was enabled, but browser restarted, debuggers are lost.
// We can't automatically re-attach due to security, but we can reset the stored state.
chrome.runtime.onStartup.addListener(() => {
     console.log("Locolite BG: Browser startup detected.");
     chrome.storage.local.get(['isEnabled'], (result) => {
         if (result.isEnabled) {
             console.log("Locolite BG: Resetting isEnabled to false on startup as debuggers are lost.");
             chrome.storage.local.set({ isEnabled: false });
         }
     });
});