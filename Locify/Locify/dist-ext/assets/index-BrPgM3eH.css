:root{font-family:system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif}body{margin:0;padding:16px;min-width:320px;min-height:100vh;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;width:320px;background-color:#f8f9fa;color:#212529}#root{padding:15px;display:flex;flex-direction:column;gap:15px}label{font-weight:500;margin-bottom:5px;display:block;color:#495057}input[type=text]{width:100%;padding:8px;margin:8px 0 16px;border:1px solid #ccc;border-radius:4px;box-sizing:border-box}.toggle-switch{display:flex;align-items:center;margin-bottom:16px}.switch{position:relative;display:inline-block;width:48px;height:24px;margin-left:10px}.switch input{opacity:0;width:0;height:0}.slider{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:#ccc;transition:.4s;border-radius:24px}.slider:before{position:absolute;content:"";height:18px;width:18px;left:3px;bottom:3px;background-color:#fff;transition:.4s;border-radius:50%}input:checked+.slider{background-color:#2196f3}input:focus+.slider{box-shadow:0 0 1px #2196f3}input:checked+.slider:before{transform:translate(24px)}.status{padding:10px;margin:10px 0;border-radius:4px;font-size:14px}.status.info{background-color:#e8f4fd;color:#0277bd}.status.success{background-color:#e8f5e9;color:#2e7d32}.status.warning{background-color:#fff8e1;color:#f57f17}.status.error{background-color:#fbe9e7;color:#c62828}.debug-note{font-size:12px;color:#666;margin-top:16px;font-style:italic}
