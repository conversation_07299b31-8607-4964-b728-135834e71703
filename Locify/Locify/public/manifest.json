{"manifest_version": 3, "name": "<PERSON><PERSON><PERSON>", "version": "1.3", "description": "Overrides live page assets with local versions.", "permissions": ["storage", "activeTab", "debugger", "scripting", "tabs"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js", "type": "module"}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon16.png", "128": "icons/icon16.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon16.png", "128": "icons/icon16.png"}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; frame-ancestors 'none'"}, "web_accessible_resources": [{"resources": ["assets/*"], "matches": ["<all_urls>"]}]}