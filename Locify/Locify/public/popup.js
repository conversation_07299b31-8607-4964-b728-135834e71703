document.addEventListener('DOMContentLoaded', function() {
  // Get UI elements
  const localServerInput = document.getElementById('localServer');
  const debugModeToggle = document.getElementById('debugMode');
  const removeCspToggle = document.getElementById('removeCsp');
  const startBtn = document.getElementById('startBtn');
  const stopBtn = document.getElementById('stopBtn');
  const statusText = document.getElementById('statusText');
  const statusContainer = document.getElementById('status');
  const logsContainer = document.getElementById('logs');
  const extensionPageWarning = document.getElementById('extensionPageWarning');
  const openNewTabBtn = document.getElementById('openNewTabBtn');
  
  // Helper function to check if a URL is a valid web page
  function isValidWebUrl(url) {
    // Return false if no URL
    if (!url) return false;
    
    try {
      // Immediately reject any chrome:// or chrome-extension:// URLs
      if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
        return false;
      }
      
      const urlObj = new URL(url);
      // Only allow http and https protocols
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch (e) {
      return false;
    }
  }
  
  // Check if current tab is valid for extension use
  checkCurrentTab();
  
  // Set up Open New Tab button
  openNewTabBtn.addEventListener('click', function() {
    chrome.tabs.create({ url: 'https://www.google.com' });
  });
  
  // Load saved settings and check extension status
  function loadSettingsAndStatus() {
    chrome.storage.local.get(['localUrl', 'debugMode', 'removeCsp', 'isEnabled'], function(result) {
      // Load settings
      localServerInput.value = result.localUrl || 'http://localhost:8001/';
      debugModeToggle.checked = result.debugMode || false;
      removeCspToggle.checked = result.removeCsp !== undefined ? result.removeCsp : true;
      
      // Check if extension is enabled
      if (result.isEnabled) {
        // Verify the status with the background script
        chrome.runtime.sendMessage({ action: 'getTabStatus' }, function(response) {
          if (response && response.isAttached) {
            updateStatus(true);
            addLog('Locify is active');
          } else {
            // If background script says it's not attached, update storage
            chrome.storage.local.set({ isEnabled: false });
            updateStatus(false);
            addLog('Locify is inactive');
          }
        });
      } else {
        updateStatus(false);
        addLog('Locify is inactive');
      }
    });
  }
  
  // Initial load
  loadSettingsAndStatus();
  
  // Helper function to check if current tab is valid
  function checkCurrentTab() {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs && tabs.length > 0) {
        const tab = tabs[0];
        
        // Diagnostic logging
        console.log("POPUP - Current tab URL:", tab.url);
        console.log("POPUP - Is valid URL:", isValidWebUrl(tab.url));
        if (tab.url) {
          try {
            const urlObj = new URL(tab.url);
            console.log("POPUP - URL protocol:", urlObj.protocol);
          } catch (e) {
            console.log("POPUP - Error parsing URL:", e.message);
          }
        }
        
        if (!tab.url || !isValidWebUrl(tab.url)) {
          // Show warning banner for non-web pages
          extensionPageWarning.style.display = 'block';
          
          // Show a helpful message in the logs
          addLog("⚠️ Please navigate to a regular website (http/https) before using Locify.", 'warn');
          startBtn.disabled = true;
        } else {
          extensionPageWarning.style.display = 'none';
        }
      }
    });
  }
  
  // Update UI based on extension status
  function updateStatus(isRunning) {
    if (isRunning) {
      statusText.textContent = 'Active';
      statusContainer.className = 'status active';
      startBtn.disabled = true;
      stopBtn.disabled = false;
      localServerInput.disabled = true;
    } else {
      statusText.textContent = 'Inactive';
      statusContainer.className = 'status inactive';
      
      // Only enable start button if on a valid web page
      chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        if (tabs && tabs.length > 0) {
          const tab = tabs[0];
          if (!tab.url || !isValidWebUrl(tab.url)) {
            startBtn.disabled = true;
            extensionPageWarning.style.display = 'block';
          } else {
            startBtn.disabled = false;
            extensionPageWarning.style.display = 'none';
          }
        } else {
          startBtn.disabled = false;
          extensionPageWarning.style.display = 'none';
        }
      });
      
      stopBtn.disabled = true;
      localServerInput.disabled = false;
    }
  }
  
  // Add log entry to the logs container
  function addLog(message, type = 'info') {
    const logEntry = document.createElement('p');
    logEntry.className = type;
    logEntry.textContent = message;
    logsContainer.appendChild(logEntry);
    logsContainer.scrollTop = logsContainer.scrollHeight;
    
    // Limit log entries to 50
    while (logsContainer.children.length > 50) {
      logsContainer.removeChild(logsContainer.firstChild);
    }
  }
  
  // Save settings to storage
  function saveSettings() {
    const settings = {
      localUrl: localServerInput.value,
      debugMode: debugModeToggle.checked,
      removeCsp: removeCspToggle.checked
    };
    
    chrome.storage.local.set(settings, function() {
      addLog('Settings saved successfully');
    });
    
    return settings;
  }
  
  // Start the extension
  startBtn.addEventListener('click', function() {
    // Check if current tab is a valid web page
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs && tabs.length > 0) {
        const tab = tabs[0];
        
        // Extra safety check - log detailed info
        console.log("START CLICK - Tab URL:", tab.url);
        if (tab.url) {
          if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
            console.log("START CLICK - Rejected chrome URL");
            addLog("Cannot run on Chrome pages. Please navigate to a regular website (http/https).", 'error');
            extensionPageWarning.style.display = 'block';
            return;
          }
        }
        
        if (!tab.url || !isValidWebUrl(tab.url)) {
          addLog("Cannot run on this page. Please navigate to a regular website (http/https).", 'error');
          extensionPageWarning.style.display = 'block';
          return;
        }
        
        // If on a valid web page, proceed with starting
        const settings = saveSettings();
        
        chrome.runtime.sendMessage({
          action: 'start',
          localServer: settings.localUrl,
          debugMode: settings.debugMode,
          removeCsp: settings.removeCsp
        }, function(response) {
          if (response && response.success) {
            // Update storage to persist the enabled state
            chrome.storage.local.set({ isEnabled: true });
            updateStatus(true);
            addLog('Locify started successfully');
          } else {
            addLog('Failed to start Locify: ' + (response?.error || 'Unknown error'), 'error');
          }
        });
      } else {
        addLog('No active tab found', 'error');
      }
    });
  });
  
  // Stop the extension
  stopBtn.addEventListener('click', function() {
    chrome.runtime.sendMessage({ action: 'stop' }, function(response) {
      if (response && response.success) {
        // Update storage to persist the disabled state
        chrome.storage.local.set({ isEnabled: false });
        updateStatus(false);
        addLog('Locify stopped successfully');
      } else {
        addLog('Failed to stop Locify: ' + (response?.error || 'Unknown error'), 'error');
      }
    });
  });
  
  // Save settings when toggles change
  debugModeToggle.addEventListener('change', saveSettings);
  removeCspToggle.addEventListener('change', saveSettings);
  
  // Listen for status updates from background script
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'statusUpdate') {
      if (request.isError) {
        addLog(request.status, 'error');
      } else {
        addLog(request.status, 'info');
      }
      
      if (request.detached) {
        // Update storage to reflect the detached state
        chrome.storage.local.set({ isEnabled: false });
        updateStatus(false);
      }
    }
    
    if (sendResponse) {
      sendResponse({ received: true });
    }
  });
  
  // Listen for tab changes and update UI accordingly
  chrome.tabs.onActivated.addListener(function() {
    // Short delay to ensure tab info is updated
    setTimeout(checkCurrentTab, 100);
  });
  
  // Listen for navigation within the same tab
  chrome.tabs.onUpdated.addListener(function(tabId, changeInfo) {
    if (changeInfo.url) {
      setTimeout(checkCurrentTab, 100);
    }
  });
  
  // Listen for storage changes to update the input live
  chrome.storage.onChanged.addListener(function(changes, namespace) {
    if (namespace === 'local' && changes.localUrl) {
      localServerInput.value = changes.localUrl.newValue || 'http://localhost:8001/';
      addLog('Local server URL updated from another tab or background.');
    }
  });
}); 