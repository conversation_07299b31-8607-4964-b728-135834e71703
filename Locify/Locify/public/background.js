// public/background.js
let attachedTabs = {}; // Store { tabId: { debuggerAttached: boolean, localUrl: string } }
let currentSettings = { isEnabled: false, localUrl: 'http://localhost:8001/' };
const DEBUGGER_VERSION = "1.3"; // Chrome Debugger Protocol version
let DEBUG_MODE = true; // Set to true by default to see diagnostic logs

// Enhanced logging
function log(message, level = 'info') {
    const prefix = "Locify BG:";
    if (level === 'error') {
        console.error(`${prefix} ${message}`);
    } else if (level === 'warn') {
        console.warn(`${prefix} ${message}`);
    } else if (level === 'debug') {
        if (DEBUG_MODE) console.log(`${prefix} [DEBUG] ${message}`);
    } else {
        console.log(`${prefix} ${message}`);
    }
}

// Load initial settings and restore state
async function initializeExtension() {
    try {
        const result = await chrome.storage.local.get(['isEnabled', 'localUrl', 'debugMode']);
        let loadedUrl = result.localUrl || '';
        if (!loadedUrl || loadedUrl === 'http://local.lvh.me:4001/') {
            loadedUrl = 'http://localhost:8001/';
            await chrome.storage.local.set({ localUrl: loadedUrl });
        }
        currentSettings.isEnabled = result.isEnabled || false;
        currentSettings.localUrl = loadedUrl;
        DEBUG_MODE = result.debugMode || false;
        log(`Initial settings loaded: ${JSON.stringify(currentSettings)}`);
        // Do NOT attach to all tabs here!
    } catch (error) {
        log(`Error initializing extension: ${error.message}`, 'error');
    }
}

// Initialize on startup with error handling
try {
    initializeExtension();
} catch (error) {
    log(`❌ [DEBUG] Critical error during initialization: ${error.message}`, 'error');
    log(`❌ [DEBUG] Initialization error stack: ${error.stack}`, 'error');
}

// Listen for storage changes (e.g., from popup)
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
        if (changes.isEnabled !== undefined) {
            const newValue = changes.isEnabled.newValue;
            const oldValue = currentSettings.isEnabled;
            currentSettings.isEnabled = newValue;
            log(`isEnabled changed: ${newValue} (was: ${oldValue})`);
            
            // If disabling globally, detach from all tabs
            if (!newValue && oldValue) {
                detachAllDebuggers();
            }
        }
        if (changes.localUrl !== undefined) {
            currentSettings.localUrl = changes.localUrl.newValue;
            log(`localUrl changed: ${currentSettings.localUrl}`);
            // Update attached tabs with new URL
            for (const tabId in attachedTabs) {
                if (attachedTabs[tabId]) {
                    attachedTabs[tabId].localUrl = currentSettings.localUrl;
                }
            }
        }
        if (changes.debugMode !== undefined) {
            DEBUG_MODE = !!changes.debugMode.newValue;
            log(`Debug mode ${DEBUG_MODE ? 'enabled' : 'disabled'}`);
        }
    }
});

// --- Debugger Handling ---

// Add a utility function to check if a URL is a valid web page
function isValidWebUrl(url) {
    // Return false if no URL
    if (!url) return false;
    
    try {
        // Immediately reject any chrome:// or chrome-extension:// URLs
        if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
            return false;
        }
        
        const urlObj = new URL(url);
        // Only allow http and https protocols
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch (e) {
        return false;
    }
}

function attachDebugger(tabId) {
    log(`🔍 [DEBUG] attachDebugger called for tab ${tabId}`);

    // Ensure we have the *latest* URL before attaching
    const urlToUse = currentSettings.localUrl;
    log(`🔍 [DEBUG] Using local URL: ${urlToUse}`);

    if (!urlToUse) {
        log("❌ [DEBUG] Cannot attach debugger: Local URL not set.", 'warn');
        sendStatusUpdate("⚠️ Error: Set Local Dev Server URL first.", true, tabId);
        return Promise.reject("Local URL not set");
    }

    // Check if already attached
    if (attachedTabs[tabId]?.debuggerAttached) {
        log(`⚠️ [DEBUG] Debugger already attached to tab ${tabId}`);
        // Ensure Fetch is enabled even if already attached (might have failed before)
        return enableFetchInterception(tabId);
    }

    log(`🔍 [DEBUG] Attempting to attach debugger to tab ${tabId}`);

    // Validate tab exists before attaching
    return new Promise((resolve, reject) => {
        log(`🔍 [DEBUG] Getting tab details for validation...`);

        chrome.tabs.get(tabId, (tab) => {
            log(`🔍 [DEBUG] chrome.tabs.get callback executed`);

            if (chrome.runtime.lastError) {
                const errMsg = `❌ [DEBUG] Failed to validate tab before attaching: ${chrome.runtime.lastError.message}`;
                log(errMsg, 'error');
                sendStatusUpdate(errMsg, true, tabId);
                reject(errMsg);
                return;
            }

            // Print detailed diagnostic info about the tab
            log(`🔍 [DEBUG] Tab validation - URL: ${tab.url || 'undefined'}`, 'debug');
            log(`🔍 [DEBUG] Tab validation - ID: ${tab.id}`, 'debug');
            log(`🔍 [DEBUG] Tab validation - Status: ${tab.status}`, 'debug');
            log(`🔍 [DEBUG] Tab validation - Is valid URL: ${isValidWebUrl(tab.url)}`, 'debug');

            // Check if the tab has a valid URL
            if (!tab.url || !isValidWebUrl(tab.url)) {
                const errMsg = "❌ [DEBUG] Cannot attach debugger to this page. Please navigate to a regular website (http/https).";
                log(errMsg, 'error');
                sendStatusUpdate(errMsg, true, tabId);
                reject(errMsg);
                return;
            }

            log(`🔍 [DEBUG] Tab validation passed, proceeding with debugger attachment...`);
            log(`🔍 [DEBUG] Using debugger protocol version: ${DEBUGGER_VERSION}`);

            // Tab exists, proceed with attaching debugger
            try {
                log(`🔍 [DEBUG] Calling chrome.debugger.attach...`);
                chrome.debugger.attach({ tabId: tabId }, DEBUGGER_VERSION, () => {
                    log(`🔍 [DEBUG] chrome.debugger.attach callback executed`);

                    if (chrome.runtime.lastError) {
                        const error = chrome.runtime.lastError.message;
                        log(`❌ [DEBUG] Debugger attach error for tab ${tabId}: ${error}`, 'error');

                        // If error indicates protocol version issue, try alternative version
                        if (error.includes('protocol version') || error.includes('version mismatch')) {
                            log(`⚠️ [DEBUG] Attempting to attach with alternative protocol version 1.2`, 'warn');
                            try {
                                chrome.debugger.attach({ tabId: tabId }, "1.2", function() {
                                    log(`🔍 [DEBUG] Alternative version attach callback executed`);

                                    if (chrome.runtime.lastError) {
                                        log(`❌ [DEBUG] Alternative version also failed: ${chrome.runtime.lastError.message}`, 'error');
                                        sendStatusUpdate(`Debugger attach failed: ${chrome.runtime.lastError.message}`, true, tabId);
                                        delete attachedTabs[tabId]; // Clean up state
                                        reject(chrome.runtime.lastError.message);
                                    } else {
                                        // Success with alternative version
                                        log(`✅ [DEBUG] Debugger attached successfully with protocol version 1.2`, 'warn');
                                        completeAttachment();
                                    }
                                });
                            } catch (altError) {
                                log(`❌ [DEBUG] Exception during alternative version attach: ${altError.message}`, 'error');
                                sendStatusUpdate(`Debugger attach failed: ${altError.message}`, true, tabId);
                                delete attachedTabs[tabId];
                                reject(altError.message);
                            }
                        } else {
                            log(`❌ [DEBUG] Non-version related error, failing attachment`, 'error');
                            sendStatusUpdate(`Debugger attach failed: ${error}`, true, tabId);
                            delete attachedTabs[tabId]; // Clean up state
                            reject(error);
                        }
                        return;
                    }

                    // Successfully attached with primary version
                    log(`✅ [DEBUG] Debugger attached successfully with primary version ${DEBUGGER_VERSION}`);
                    completeAttachment();
                });
            } catch (attachError) {
                log(`❌ [DEBUG] Exception during chrome.debugger.attach: ${attachError.message}`, 'error');
                log(`❌ [DEBUG] Attach exception stack: ${attachError.stack}`, 'error');
                sendStatusUpdate(`Critical debugger error: ${attachError.message}`, true, tabId);
                delete attachedTabs[tabId];
                reject(attachError.message);
                return;
            }

            // Helper function to complete attachment process
            function completeAttachment() {
                log(`🔍 [DEBUG] Completing attachment process for tab ${tabId}`);
                attachedTabs[tabId] = { debuggerAttached: true, localUrl: urlToUse }; // Use current URL
                log(`🔍 [DEBUG] Updated attachedTabs state:`, Object.keys(attachedTabs));

                // Add listeners *after* successful attachment, ensuring they are added only once
                if (!chrome.debugger.onEvent.hasListener(onDebuggerEvent)) {
                    log(`🔍 [DEBUG] Adding onEvent listener`);
                    chrome.debugger.onEvent.addListener(onDebuggerEvent);
                } else {
                    log(`🔍 [DEBUG] onEvent listener already exists`);
                }

                if (!chrome.debugger.onDetach.hasListener(onDebuggerDetach)) {
                    log(`🔍 [DEBUG] Adding onDetach listener`);
                    chrome.debugger.onDetach.addListener(onDebuggerDetach);
                } else {
                    log(`🔍 [DEBUG] onDetach listener already exists`);
                }

                log(`🔍 [DEBUG] Calling enableFetchInterception...`);
                enableFetchInterception(tabId)
                    .then((result) => {
                        log(`✅ [DEBUG] enableFetchInterception succeeded`);
                        resolve(result);
                    })
                    .catch((error) => {
                        log(`❌ [DEBUG] enableFetchInterception failed: ${error}`, 'error');
                        reject(error);
                    });
            }
        });
    });
}

function enableFetchInterception(tabId) {
    log(`🔍 [DEBUG] enableFetchInterception called for tab ${tabId}`);

    return new Promise((resolve, reject) => {
        log(`🔍 [DEBUG] Sending Fetch.enable command...`);

        chrome.debugger.sendCommand({ tabId: tabId }, "Fetch.enable", {
            patterns: [
                { resourceType: "Document", requestStage: "HeadersReceived" }
            ]
        }, (result) => {
            log(`🔍 [DEBUG] Fetch.enable callback executed`);

            if (chrome.runtime.lastError) {
                const error = chrome.runtime.lastError.message;
                log(`❌ [DEBUG] Fetch.enable error for tab ${tabId}: ${error}`, 'error');

                // If it's a permissions issue, try with NetworkRequest domain as fallback
                if (error.includes('permission') || error.includes('not allowed')) {
                    log("⚠️ [DEBUG] Fetch API access denied. Attempting fallback to Network domain", 'warn');
                    chrome.debugger.sendCommand({ tabId: tabId }, "Network.enable", {}, (result) => {
                        log(`🔍 [DEBUG] Network.enable fallback callback executed`);

                        if (chrome.runtime.lastError) {
                            log(`❌ [DEBUG] Network.enable fallback also failed: ${chrome.runtime.lastError.message}`, 'error');
                            sendStatusUpdate(`Resource interception failed: ${chrome.runtime.lastError.message}`, true, tabId);

                            log(`🔍 [DEBUG] Attempting to detach debugger after Network.enable failure...`);
                            chrome.debugger.detach({ tabId: tabId }).catch(() => {});
                            reject(`Resource interception failed: ${chrome.runtime.lastError.message}`);
                        } else {
                            log(`✅ [DEBUG] Using Network domain as fallback for tab ${tabId}`, 'warn');
                            attachedTabs[tabId].usingNetworkFallback = true;
                            sendStatusUpdate("Overrides active with alternative method. Reload page.", false, tabId);
                            resolve();
                        }
                    });
                } else {
                    log(`❌ [DEBUG] Non-permission related Fetch.enable error`, 'error');
                    sendStatusUpdate(`Fetch interception failed: ${error}`, true, tabId);

                    log(`🔍 [DEBUG] Attempting to detach debugger after Fetch.enable failure...`);
                    // Attempt to detach if Fetch.enable failed, but don't let it block the reject
                    chrome.debugger.detach({ tabId: tabId }).catch(() => {});
                    reject(`Fetch.enable failed: ${error}`);
                }
            } else {
                log(`✅ [DEBUG] Fetch interception enabled for tab ${tabId}`);
                attachedTabs[tabId].usingNetworkFallback = false;
                sendStatusUpdate("✅ Overrides active. Reload page if needed.", false, tabId);
                resolve();
            }
        });
    });
}


function detachDebugger(tabId) {
    return new Promise((resolve) => {
         if (!attachedTabs[tabId]?.debuggerAttached) {
             // console.log(`Locolite BG: Debugger not attached to tab ${tabId}, skipping detach.`);
             resolve(); // Nothing to do
             return;
         }
        console.log(`Locolite BG: Detaching debugger from tab ${tabId}`);

         // It's generally safer to attempt removal even if unsure if they were added for this specific tab
         chrome.debugger.onEvent.removeListener(onDebuggerEvent);
         chrome.debugger.onDetach.removeListener(onDebuggerDetach);

         chrome.debugger.detach({ tabId: tabId }, () => {
            if (chrome.runtime.lastError) {
                // Log error but resolve anyway, as the debugger might be gone already
                console.error(`Locolite BG: Debugger detach error for tab ${tabId}:`, chrome.runtime.lastError.message);
            } else {
                console.log(`Locolite BG: Debugger detached from tab ${tabId}`);
            }
            delete attachedTabs[tabId];
            // Only send status update if disabling wasn't global, otherwise it's redundant
             if (currentSettings.isEnabled) {
                 sendStatusUpdate("Overrides disabled.", false, tabId);
             }
            resolve();
        });
    });
}

function detachAllDebuggers() {
    console.log("Locolite BG: Detaching from all tabs.");
    const detachPromises = Object.keys(attachedTabs).map(tabIdStr => {
        const tabId = parseInt(tabIdStr, 10);
        if (attachedTabs[tabId]?.debuggerAttached) {
            return detachDebugger(tabId);
        }
        return Promise.resolve();
    });
    return Promise.all(detachPromises).then(() => {
        // Send a general status update after detaching all
        sendStatusUpdate("Overrides disabled globally.", false, null);
    });
}

// --- Event Listeners ---

// Listen for messages from Popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    try {
        log("🔍 [DEBUG] Received message:", JSON.stringify(message));

        if (message.action === "start") {
            log("🔍 [DEBUG] Processing START action");
            chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
                try {
                    log("🔍 [DEBUG] Tab query completed, tabs:", tabs?.length || 0);

                    if (!tabs || tabs.length === 0) {
                        log("❌ [DEBUG] No active tab found", 'error');
                        sendResponse({ success: false, error: "No active tab found" });
                        return;
                    }

                    const tab = tabs[0];
                    const tabId = tab.id;
                    log(`🔍 [DEBUG] Active tab details - ID: ${tabId}, URL: ${tab.url}, Status: ${tab.status}`);

                    if (!tabId) {
                        log("❌ [DEBUG] No valid tab ID found", 'error');
                        sendResponse({ success: false, error: "No valid tab ID found" });
                        return;
                    }

                    // Check if tab is in a valid state
                    if (tab.status === 'loading') {
                        log("⚠️ [DEBUG] Tab is still loading, this might cause issues", 'warn');
                    }

                    log("🔍 [DEBUG] Saving settings to storage...");
                    // Save settings first
                    await chrome.storage.local.set({
                        localServer: message.localServer,
                        debugMode: message.debugMode,
                        removeCsp: message.removeCsp,
                        isEnabled: true
                    });
                    log("✅ [DEBUG] Settings saved successfully");

                    // Update current settings
                    currentSettings.isEnabled = true;
                    currentSettings.localUrl = message.localServer;
                    DEBUG_MODE = message.debugMode;

                    log(`🔍 [DEBUG] About to attach debugger to tab ${tabId} with URL: ${message.localServer}`);
                    log(`🔍 [DEBUG] Current attachedTabs state:`, Object.keys(attachedTabs));

                    try {
                        log("🔍 [DEBUG] Calling attachDebugger function...");
                        await attachDebugger(tabId);
                        log("✅ [DEBUG] Debugger attached successfully - sending success response");
                        sendResponse({ success: true });
                    } catch (error) {
                        log(`❌ [DEBUG] Error attaching debugger: ${error}`, 'error');
                        log(`❌ [DEBUG] Error stack: ${error.stack}`, 'error');

                        // Reset state on error
                        await chrome.storage.local.set({ isEnabled: false });
                        currentSettings.isEnabled = false;
                        delete attachedTabs[tabId]; // Clean up any partial state

                        sendResponse({ success: false, error: error.toString() });
                    }
                } catch (err) {
                    log(`❌ [DEBUG] Error handling start message: ${err.message}`, 'error');
                    log(`❌ [DEBUG] Error stack: ${err.stack}`, 'error');
                    sendResponse({ success: false, error: `Internal error: ${err.message}` });
                }
            });
            return true; // Will respond asynchronously
        }
        else if (message.action === "stop") {
            chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
                try {
                    // Update settings first
                    await chrome.storage.local.set({ isEnabled: false });
                    currentSettings.isEnabled = false;
                    
                    if (tabs && tabs.length > 0) {
                        const tabId = tabs[0].id;
                        if (tabId) {
                            // Then detach debugger
                            try {
                                await detachDebugger(tabId);
                                log("Debugger detached successfully");
                                sendResponse({ success: true });
                            } catch (error) {
                                log(`Error detaching debugger: ${error}`, 'error');
                                sendResponse({ success: false, error: error.toString() });
                            }
                            return;
                        }
                    }
                    
                    // If no active tab or no tab ID, detach from all tabs
                    try {
                        await detachAllDebuggers();
                        log("All debuggers detached successfully");
                        sendResponse({ success: true });
                    } catch (error) {
                        log(`Error detaching all debuggers: ${error}`, 'error');
                        sendResponse({ success: false, error: error.toString() });
                    }
                } catch (err) {
                    log(`Error handling stop message: ${err.message}`, 'error');
                    sendResponse({ success: false, error: `Internal error: ${err.message}` });
                }
            });
            return true; // Will respond asynchronously
        }
        else if (message.action === "getTabStatus") {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                try {
                    if (tabs && tabs.length > 0) {
                        const tabId = tabs[0].id;
                        const isAttached = attachedTabs[tabId]?.debuggerAttached ?? false;
                        sendResponse({ isAttached: isAttached });
                    } else {
                        sendResponse({ isAttached: false, error: "No active tab" });
                    }
                } catch (err) {
                    log(`Error handling getTabStatus message: ${err.message}`, 'error');
                    sendResponse({ isAttached: false, error: `Internal error: ${err.message}` });
                }
            });
            return true; // Will respond asynchronously
        }
    } catch (err) {
        log(`Error in message handler: ${err.message}`, 'error');
        if (sendResponse) {
            sendResponse({ success: false, error: `Internal error: ${err.message}` });
        }
    }
});

// Listen for Debugger Events (like network requests)
function onDebuggerEvent(debuggeeId, message, params) {
    const tabId = debuggeeId.tabId;
    log(`[DEBUG] onDebuggerEvent: message=${message}, tabId=${tabId}`);
    if (message === "Fetch.requestPaused") {
        const { requestId, request, resourceType } = params;
        log(`[DEBUG] Fetch.requestPaused for ${request.url} (type: ${resourceType}) on tab ${tabId}`);
        continueRequest(tabId, requestId);
    }
}

// Recursively try paths until one works
function redirectToFirstAvailablePath(tabId, requestId, paths, index = 0) {
    if (index >= paths.length) {
        // If we've tried all paths, continue the original request
        log("⚠️ All redirect paths failed, continuing with original request", 'warn');
        return continueRequest(tabId, requestId);
    }
    
    const currentPath = paths[index];
    log(`🔄 Trying path ${index + 1}/${paths.length}: ${currentPath}`, 'debug');
    
    // Try to HEAD request the path first to verify it exists
    fetch(currentPath, { method: 'HEAD' })
        .then(response => {
            if (response.ok) {
                // If found, redirect to it
                log(`✅ Found resource at ${currentPath}, redirecting`, 'info');
                
                // Send a status update to the popup
                sendStatusUpdate(`✅ Redirecting to local file: ${currentPath}`, false, tabId);
                
                // Verify the file exists and is accessible
                return fetch(currentPath)
                    .then(verifyResponse => {
                        if (verifyResponse.ok) {
                            log(`✅ Verified local file is accessible: ${currentPath}`, 'info');
                            return chrome.debugger.sendCommand(
                                { tabId },
                                "Fetch.fulfillRequest",
                                {
                                    requestId,
                                    responseCode: 302, // Found/Redirect
                                    responseHeaders: [
                                        { name: "Location", value: currentPath },
                                        { name: "Access-Control-Allow-Origin", value: "*" },
                                        { name: "Cache-Control", value: "no-cache, no-store" }
                                    ]
                                }
                            );
                        } else {
                            log(`❌ Local file exists but is not accessible: ${currentPath}`, 'error');
                            sendStatusUpdate(`❌ Local file not accessible: ${currentPath}`, true, tabId);
                            return redirectToFirstAvailablePath(tabId, requestId, paths, index + 1);
                        }
                    })
                    .catch(error => {
                        log(`❌ Error verifying local file: ${error.message}`, 'error');
                        sendStatusUpdate(`❌ Error accessing local file: ${error.message}`, true, tabId);
                        return redirectToFirstAvailablePath(tabId, requestId, paths, index + 1);
                    });
            } else {
                // If not found, try the next path
                log(`❌ Resource not found at ${currentPath} (status: ${response.status}), trying next path`, 'debug');
                return redirectToFirstAvailablePath(tabId, requestId, paths, index + 1);
            }
        })
        .catch(error => {
            // If fetch fails, try the next path
            log(`❌ Failed to check ${currentPath}: ${error.message}`, 'debug');
            return redirectToFirstAvailablePath(tabId, requestId, paths, index + 1);
        });
}

// Helper function to continue a request
function continueRequest(tabId, requestId) {
    chrome.debugger.sendCommand(
        { tabId }, 
        "Fetch.continueRequest", 
        { requestId }
    ).catch(e => log(`Error continuing request: ${e.message || e}`, 'error'));
}

// Handle Debugger Detach Event (e.g., user closed DevTools, tab closed)
function onDebuggerDetach(debuggeeId, reason) {
    const tabId = debuggeeId.tabId;
    console.log(`Locolite BG: Debugger detached from tab ${tabId}. Reason: ${reason}`);
    if (attachedTabs[tabId]) {
        delete attachedTabs[tabId];
        // Update popup status ONLY if the extension is still globally enabled
        // And check if the detach reason suggests user action (e.g., closed devtools) vs programmatic detach
         if (currentSettings.isEnabled && reason !== 'target_closed') {
            // Inform the popup that this specific tab was detached, possibly by user action
            sendStatusUpdate("Debugger detached (e.g., DevTools closed). Re-enable toggle if needed.", true, tabId, true);

             // Since debugger detached unexpectedly for the active tab, maybe force the global toggle state off?
             // This prevents the toggle staying 'on' when debugging is actually stopped for the active tab.
             // Let's check if the detached tab is the active one.
             chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                 if (tabs && tabs.length > 0 && tabs[0].id === tabId) {
                     console.log("Locolite BG: Detached from active tab, forcing toggle off in storage.");
                     chrome.storage.local.set({ isEnabled: false }); // This will trigger the storage listener
                 }
             });
         }
    }
}


// --- Core Logic: Modify HTML ---

async function handleInterceptedRequest(tabId, requestId, originalUrl, localDevUrl) {
    log(`Handling intercepted request for ${originalUrl} on tab ${tabId} with local URL ${localDevUrl}`, 'debug');

    try {
        // 1. Get the original response body
        const response = await chrome.debugger.sendCommand(
            { tabId: tabId },
            "Fetch.getResponseBody",
            { requestId: requestId }
        );

        let body;
        if (response.base64Encoded) {
            try {
                // Use TextDecoder for more reliable UTF-8 decoding
                const binaryString = atob(response.body);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                const decoder = new TextDecoder('utf-8');
                body = decoder.decode(bytes);
            } catch (e) {
                log(`Base64 decoding error: ${e.message}`, 'warn');
                // Fallback to the previous method
                try {
                    body = decodeURIComponent(
                        atob(response.body)
                            .split('')
                            .map(function(c) {
                                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                            })
                            .join('')
                    );
                } catch (e2) {
                    log(`Fallback decoding also failed: ${e2.message}`, 'error');
                    // Last resort: just try simple atob
                    body = atob(response.body);
                }
            }
        } else {
            body = response.body; // Already UTF-8 text
        }

        // 2. Modify the HTML body
        // Ensure localDevUrl is a valid base URL (ends with / if needed)
        const validatedLocalUrl = localDevUrl.endsWith('/') ? localDevUrl : localDevUrl + '/';
        const modifiedBody = rewriteAssetPaths(body, originalUrl, validatedLocalUrl);

        // 3. Fulfill the request with the modified body
        // Use TextEncoder for more reliable UTF-8 encoding before base64
        let encodedBody;
        try {
            const encoder = new TextEncoder();
            const bytes = encoder.encode(modifiedBody);
            encodedBody = btoa(
                Array.from(bytes)
                    .map(byte => String.fromCharCode(byte))
                    .join('')
            );
        } catch (e) {
            log(`Error encoding modified body: ${e.message}`, 'warn');
            // Fallback to the previous method
            encodedBody = btoa(unescape(encodeURIComponent(modifiedBody)));
        }

        await chrome.debugger.sendCommand(
            { tabId: tabId },
            "Fetch.fulfillRequest",
            {
                requestId: requestId,
                responseCode: 200,
                responseHeaders: [
                    { name: 'Content-Type', value: 'text/html; charset=utf-8' },
                    // Add Access-Control-Allow-Origin header to handle CORS issues
                    { name: 'Access-Control-Allow-Origin', value: '*' }
                ],
                body: encodedBody
            }
        );
        log(`Successfully fulfilled request ${requestId} with modified content`, 'debug');

    } catch (error) {
        log(`Error handling intercepted request ${requestId} for tab ${tabId}: ${error.message || error}`, 'error');
        sendStatusUpdate(`Error processing override: ${error.message || error}`, true, tabId);
        
        // If error occurs, try to let the original request continue
        try {
            await chrome.debugger.sendCommand({ tabId: tabId }, "Fetch.continueRequest", { requestId });
            log(`Continued original request after error for ${requestId}`, 'warn');
        } catch (continueError) {
            log(`Error trying to continue request ${requestId} after failure: ${continueError.message || continueError}`, 'error');
            
            // If continuing also fails, try to fail request gracefully
            try {
                await chrome.debugger.sendCommand(
                    { tabId: tabId },
                    "Fetch.failRequest",
                    { requestId: requestId, errorReason: 'Failed' }
                );
            } catch (failError) {
                log(`Error failing request ${requestId} after primary error: ${failError.message || failError}`, 'error');
                // At this point, we've tried everything - let Chrome handle the request timeout
            }
        }
    }
}

function rewriteAssetPaths(htmlContent, originalPageUrlString, localDevBaseUrl) {
    log(`Rewriting assets in HTML for ${originalPageUrlString} to use ${localDevBaseUrl}`, 'debug');
    // Replace /dist-<hash>-<filename> with localDevBaseUrl/<filename>
    return htmlContent.replace(
        /((src|href)\s*=\s*["'])\/dist-[^/]+-([\w.-]+\.(js|css))(["'])/g,
        (match, prefix, attr, filename, suffix) => {
            return `${prefix}${localDevBaseUrl}${filename}${suffix}`;
        }
    );
}

// --- Utility ---
function sendStatusUpdate(status, isError = false, tabId = null, detached = false) {
    // Send to popup (if open)
    console.log(`Locolite BG: Status update${tabId ? ` for tab ${tabId}` : ''}: ${status}`);
    chrome.runtime.sendMessage({
        action: "statusUpdate",
        status: status,
        isError: isError,
        detached: detached // Inform popup if disabling occurred unexpectedly for the tab
    }).catch(e => { /* Ignore error if popup isn't open */ });
}

// --- Cleanup on Extension Uninstall/Disable ---
chrome.runtime.onSuspend.addListener(() => {
    console.log("Locolite BG: Extension suspending. Detaching all debuggers.");
    detachAllDebuggers();
});

// --- Initial check on startup ---
// If extension was enabled, but browser restarted, debuggers are lost.
// We can't automatically re-attach due to security, but we can reset the stored state.
chrome.runtime.onStartup.addListener(() => {
     console.log("Locolite BG: Browser startup detected.");
     chrome.storage.local.get(['isEnabled'], (result) => {
         if (result.isEnabled) {
             console.log("Locolite BG: Resetting isEnabled to false on startup as debuggers are lost.");
             chrome.storage.local.set({ isEnabled: false });
         }
     });
});

// --- Additional Service Worker Lifecycle Handling ---
// Chrome extensions with service workers may be terminated and restarted
// These handlers help manage that lifecycle

// Log when service worker starts up
chrome.runtime.onInstalled.addListener((details) => {
    log(`Extension ${details.reason === 'install' ? 'installed' : 'updated'}`, 'info');
    
    // If debug mode is on, immediately print debug info
    chrome.storage.local.get(['debugMode', 'localUrl'], (result) => {
        if (result.debugMode) {
            DEBUG_MODE = true;
            log('Debug mode enabled on startup', 'debug');
            
            // Verify extension status
            verifyExtensionStatus();
            
            // Check if any tabs have the debugger attached
            chrome.tabs.query({}, (tabs) => {
                log(`Found ${tabs.length} tabs at startup`, 'debug');
                if (Object.keys(attachedTabs).length > 0) {
                    log(`Warning: attachedTabs has ${Object.keys(attachedTabs).length} entries at startup`, 'warn');
                }
            });
        }
    });
});

// Keep alive - ping every 20 seconds to prevent service worker from being terminated
// Only when the extension is actively being used
setInterval(() => {
    if (Object.keys(attachedTabs).length > 0 || currentSettings.isEnabled) {
        log('Keeping service worker alive', 'debug');
    }
}, 20000);

// Check if Fetch API is supported (for debugging)
try {
    if (DEBUG_MODE && typeof fetch === 'function') {
        log('Fetch API is supported', 'debug');
    }
} catch (e) {
    log('Fetch API not supported: ' + e.message, 'error');
}

// Add unhandled error logging - using self instead of window in service worker context
self.addEventListener('error', function(event) {
    log(`❌ [DEBUG] Unhandled error: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
    log(`❌ [DEBUG] Error stack: ${event.error?.stack}`, 'error');

    // Try to prevent browser crash by preventing default
    event.preventDefault();
});

self.addEventListener('unhandledrejection', function(event) {
    log(`❌ [DEBUG] Unhandled promise rejection: ${event.reason}`, 'error');
    log(`❌ [DEBUG] Rejection stack: ${event.reason?.stack}`, 'error');

    // Try to prevent browser crash by preventing default
    event.preventDefault();

    // If this is related to debugger operations, try to clean up
    if (event.reason && typeof event.reason === 'string') {
        if (event.reason.includes('debugger') || event.reason.includes('Debugger')) {
            log(`⚠️ [DEBUG] Debugger-related rejection detected, attempting cleanup...`, 'warn');
            // Attempt to detach all debuggers to prevent further issues
            try {
                detachAllDebuggers().catch(() => {
                    log(`❌ [DEBUG] Failed to detach debuggers during cleanup`, 'error');
                });
            } catch (cleanupError) {
                log(`❌ [DEBUG] Error during debugger cleanup: ${cleanupError}`, 'error');
            }
        }
    }
});

// Function to handle HTML documents and remove CSP meta tags and headers
async function handleHtmlDocument(tabId, requestId, url) {
    log(`Processing HTML document: ${url}`, 'info');
    
    try {
        // First, get the response body directly since getResponseDetails is not supported
        const response = await chrome.debugger.sendCommand(
            { tabId: tabId },
            "Fetch.getResponseBody",
            { requestId: requestId }
        ).catch(error => {
            log(`Error getting response body: ${error.message}`, 'error');
            throw error;
        });

        let html;
        if (response.base64Encoded) {
            try {
                // Use TextDecoder for more reliable UTF-8 decoding
                const binaryString = atob(response.body);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                const decoder = new TextDecoder('utf-8');
                html = decoder.decode(bytes);
            } catch (e) {
                log(`HTML decoding error: ${e.message}`, 'warn');
                html = atob(response.body); // Fallback
            }
        } else {
            html = response.body; // Already UTF-8 text
        }

        // Check if there's a CSP meta tag
        const hasCspMetaTag = html.includes('Content-Security-Policy') || 
                              html.includes('http-equiv="Content-Security-Policy"');
        
        // Create modified headers
        const headers = [
            { name: 'Content-Type', value: 'text/html; charset=utf-8' },
            { name: 'Access-Control-Allow-Origin', value: '*' }
        ];
        
        // Add a permissive CSP header
        headers.push({ 
            name: 'Content-Security-Policy', 
            value: "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:;"
        });
        
        // Check if HTML needs modification for meta tag removal
        let modifiedHtml = html;
        if (hasCspMetaTag) {
            log('Found CSP meta tag, removing it', 'info');
            
            // Remove the CSP meta tag using regex
            modifiedHtml = html.replace(
                /<meta\s+[^>]*http-equiv\s*=\s*(['"])Content-Security-Policy\1[^>]*>|<meta\s+[^>]*Content-Security-Policy[^>]*>/gi, 
                '<!-- CSP meta tag removed by Locify -->'
            );

            log('CSP meta tag removed successfully', 'info');
        }

        // Encode the HTML (modified or original)
        let encodedBody;
        try {
            const encoder = new TextEncoder();
            const bytes = encoder.encode(modifiedHtml);
            encodedBody = btoa(
                Array.from(bytes)
                    .map(byte => String.fromCharCode(byte))
                    .join('')
            );
        } catch (e) {
            log(`Error encoding HTML: ${e.message}`, 'warn');
            encodedBody = btoa(unescape(encodeURIComponent(modifiedHtml)));
        }

        // Fulfill with the modified response
        await chrome.debugger.sendCommand(
            { tabId: tabId },
            "Fetch.fulfillRequest",
            {
                requestId: requestId,
                responseCode: 200,
                responseHeaders: headers,
                body: encodedBody
            }
        ).catch(error => {
            log(`Error fulfilling request: ${error.message}`, 'error');
            throw error;
        });
        
        log('Modified HTML document successfully fulfilled', 'info');
    } catch (error) {
        log(`Error handling HTML document: ${error.message || error}`, 'error');
        
        // If we get error -32601, it means the command is not supported
        // In this case, we should just continue the request
        if (error.message && error.message.includes('-32601')) {
            log('Debugger command not supported, continuing original request', 'warn');
            continueRequest(tabId, requestId);
            return;
        }
        
        // For other errors, try to continue the request
        try {
            await continueRequest(tabId, requestId);
        } catch (continueError) {
            log(`Error continuing request after failure: ${continueError.message}`, 'error');
        }
    }
}

// Add a function to verify the extension is working
function verifyExtensionStatus() {
    log("🔍 Verifying extension status...", 'info');
    
    // Check if we have a local URL set
    if (!currentSettings.localUrl) {
        log("❌ No local URL configured", 'error');
        return false;
    }
    
    // Check if the local server is accessible
    fetch(currentSettings.localUrl, { method: 'HEAD' })
        .then(response => {
            if (response.ok) {
                log(`✅ Local server is accessible at ${currentSettings.localUrl}`, 'info');
                sendStatusUpdate(`✅ Local server is accessible`, false);
            } else {
                log(`❌ Local server returned status ${response.status}`, 'error');
                sendStatusUpdate(`❌ Local server returned status ${response.status}`, true);
            }
        })
        .catch(error => {
            log(`❌ Cannot access local server: ${error.message}`, 'error');
            sendStatusUpdate(`❌ Cannot access local server: ${error.message}`, true);
        });
}