// Base64 to PNG converter
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base64 data for a simple blue square with 'L' icon (16x16)
const icon16Base64 = 'iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA0UlEQVQ4T2NkIAKwYFP0f/P/K4x9mPL/M7IwIAt8+/WLgUWIm+G4vbLU/82vphNjACPIgANbHzFsYmFl2G+rLPFz081ZuF3wf/WH44zcnAyHrJXE/m6+PY9gGPxf+f4oCw83w0FrJdH/m2/PJ2jA/+UfjrLy8jActFIS+b/p1gLcBvxf+v4IKx8Pw0FLRZH/G28tJCIO/h9m5edlOGihIPJ//a2FDIyMhHTj8QIjw2EzeaH/a28tImwAvAyY++4wm5AgwwEjWcH/q28twW8AkXkBAEcffTF6L36xAAAAAElFTkSuQmCC';

// Save base64 data as PNG file
function saveBase64AsPng(base64Data, outputPath) {
  // Remove data URL prefix if present
  const base64Image = base64Data.replace(/^data:image\/png;base64,/, '');
  
  // Convert base64 to buffer
  const imageBuffer = Buffer.from(base64Image, 'base64');
  
  // Write buffer to file
  fs.writeFileSync(outputPath, imageBuffer);
  console.log(`Created: ${outputPath}`);
}

// Save icon16.png
saveBase64AsPng(icon16Base64, path.join(__dirname, 'icon16.png'));

console.log('Icon created successfully!'); 