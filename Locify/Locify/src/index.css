/* src/index.css */
/* --- PASTE THE index.css CONTENT FROM THE PREVIOUS EXAMPLE HERE --- */
/* (The one starting with body { margin: 0; ... } ) */
:root {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

body {
  margin: 0;
  padding: 16px;
  min-width: 320px;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 320px; /* Popup width */
  /* removed padding from body, added to #root in popup.html or App container */
  background-color: #f8f9fa; /* Light background */
  color: #212529; /* Dark text */
}

/* Apply padding and layout to the root element where <PERSON>act mounts */
#root {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px; /* Spacing between elements */
}

label {
    font-weight: 500;
    margin-bottom: 5px;
    display: block; /* Ensure label is above its control */
     color: #495057;
}

input[type="text"] {
    width: 100%;
    padding: 8px;
    margin: 8px 0 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

/* Toggle switch styles */
.toggle-switch {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  margin-left: 10px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

/* Status styles */
.status {
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
  font-size: 14px;
}

.status.info {
  background-color: #e8f4fd;
  color: #0277bd;
}

.status.success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status.warning {
  background-color: #fff8e1;
  color: #f57f17;
}

.status.error {
  background-color: #fbe9e7;
  color: #c62828;
}

/* Debug note styles */
.debug-note {
  font-size: 12px;
  color: #666;
  margin-top: 16px;
  font-style: italic;
}

/* Add styles for new UI elements at the end of the file */

/* Diagnostics panel styling */
.diagnostics-panel {
  margin-top: 1rem;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f8f8f8;
}

.diagnostics-panel h3 {
  margin-top: 0;
  font-size: 1rem;
  color: #333;
}

.diagnostics-panel pre {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f0f0f0;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.clear-button {
  background-color: #5c6bc0;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.clear-button:hover {
  background-color: #3f51b5;
}

/* Debug mode toggle */
.toggle-debug-mode {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Smaller switch for debug mode */
.switch.small {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch.small .slider {
  border-radius: 20px;
}

.switch.small .slider:before {
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
}

.switch.small input:checked + .slider:before {
  transform: translateX(20px);
}

/* Compatibility warning */
.compatibility-warning {
  margin-top: 1rem;
  padding: 0.5rem;
  border: 1px solid #ff9800;
  border-radius: 4px;
  background-color: #fff3e0;
  color: #e65100;
  font-size: 0.85rem;
}

/* Improve accessibility and general styling */
input[type="text"],
input[type="checkbox"] {
  box-sizing: border-box;
}

input[type="text"]:focus,
input[type="checkbox"]:focus,
button:focus {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Focus styles for slider */
.switch input:focus + .slider {
  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.4);
}

/* High contrast mode support */
@media (forced-colors: active) {
  .slider {
    border: 1px solid CanvasText;
  }
  
  .slider:before {
    background-color: CanvasText;
  }
  
  input:checked + .slider:before {
    background-color: HighlightText;
  }
  
  .status,
  .compatibility-warning,
  .diagnostics-panel {
    border: 1px solid CanvasText;
  }
}